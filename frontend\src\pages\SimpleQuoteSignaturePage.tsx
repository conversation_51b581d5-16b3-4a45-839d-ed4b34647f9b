import React, { useState, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaArrowLeft, FaSignature, FaEraser, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaFileAlt } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import { MobileContainer, MobilePageHeader } from '../components/common/MobileUtils';
import SignatureCanvas from 'react-signature-canvas';
import simpleOfferteService from '../services/simpleOfferteService';

interface CustomerData {
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  customer_address: string;
  customer_city: string;
  customer_postal_code: string;
}

const SimpleQuoteSignaturePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const signatureRef = useRef<SignatureCanvas>(null);
  
  // Get quote configuration from previous page
  const quoteConfig = location.state?.quoteConfig;
  
  const [signatureData, setSignatureData] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSignature, setShowSignature] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Redirect if no quote config
  if (!quoteConfig) {
    navigate('/simple-quotes');
    return null;
  }

  const { customerData, category, calculations } = quoteConfig;

  const clearSignature = () => {
    if (signatureRef.current) {
      signatureRef.current.clear();
      setSignatureData('');
    }
  };

  const saveSignature = () => {
    if (signatureRef.current) {
      const signature = signatureRef.current.toDataURL();
      setSignatureData(signature);
      setShowSignature(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!signatureData) {
      alert('Handtekening is verplicht');
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert to backend format
      const backendFormat = simpleOfferteService.convertToBackendFormat(
        quoteConfig.category,
        quoteConfig.paymentTerms
      );

      // Create the offerte with customer data
      const offerteData = {
        ...customerData,
        category: backendFormat.category,
        payment_terms: backendFormat.payment_terms,
        discount_percentage: quoteConfig.discountPercentage,
        videodoorbell_free: quoteConfig.videodoorbellFree,
        videodoorbell_paid: quoteConfig.videodoorbellPaid,
        signature_data: signatureData,
        ...quoteConfig.extraProducts
      };

      const result = await simpleOfferteService.createOfferte(offerteData);

      // Navigate to success page
      navigate('/simple-quotes/success', {
        state: {
          customerId: result.customer.id,
          customerName: result.customer.name,
          documentId: result.document.id,
          documentName: result.document.name
        }
      });

    } catch (error) {
      console.error('Error submitting quote:', error);
      alert('Er is een fout opgetreden bij het opslaan van de offerte');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getCategoryTitle = () => {
    switch (category) {
      case 'alarm': return 'Alarm Systeem';
      case 'cameras': return 'Camera Systeem';
      case 'alarm_cameras': return 'Alarm + Camera Systeem';
      default: return 'Offerte';
    }
  };

  return (
    <MobileContainer>
      <Breadcrumbs />
      
      <MobilePageHeader
        title="Offerte Ondertekenen"
        subtitle={`Controleer uw offerte en onderteken, ${customerData.customer_name}`}
      />

      <div className="space-y-6">
        {/* Header with progress */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
          >
            <FaArrowLeft className="mr-2" />
            Terug naar configuratie
          </button>

          {/* Progress indicator */}
          <div className="flex items-center space-x-2">
            <div className="flex items-center">
              <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs">
                ✓
              </div>
              <span className="ml-1 text-xs text-green-600">Gegevens</span>
            </div>
            <div className="w-4 h-0.5 bg-green-500"></div>
            <div className="flex items-center">
              <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs">
                ✓
              </div>
              <span className="ml-1 text-xs text-green-600">Systeem</span>
            </div>
            <div className="w-4 h-0.5 bg-green-500"></div>
            <div className="flex items-center">
              <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs">
                ✓
              </div>
              <span className="ml-1 text-xs text-green-600">Config</span>
            </div>
            <div className="w-4 h-0.5 bg-blue-500"></div>
            <div className="flex items-center">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs">
                4
              </div>
              <span className="ml-1 text-xs text-blue-600">Onderteken</span>
            </div>
          </div>
        </div>

        {/* Quote summary */}
        {calculations && (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-blue-900 flex items-center text-lg">
                <FaFileAlt className="mr-2" />
                Offerte Overzicht
              </h3>
              <button
                onClick={() => setShowPreview(!showPreview)}
                className="text-blue-600 hover:text-blue-800 flex items-center text-sm"
              >
                <FaEye className="mr-1" />
                {showPreview ? 'Verberg details' : 'Toon details'}
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-900">
                  €{calculations.final_installation_cost.toFixed(2)}
                </div>
                <div className="text-sm text-blue-700">Installatiekosten</div>
                {calculations.discount_amount > 0 && (
                  <div className="text-xs text-green-600 mt-1">
                    Korting: €{calculations.discount_amount.toFixed(2)}
                  </div>
                )}
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-900">
                  €{calculations.total_monthly_cost.toFixed(2)}
                </div>
                <div className="text-sm text-blue-700">Per maand</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-900">
                  {getCategoryTitle()}
                </div>
                <div className="text-sm text-blue-700">Gekozen systeem</div>
              </div>
            </div>

            {showPreview && (
              <div className="mt-6 pt-6 border-t border-blue-200">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-blue-900 mb-3">Klantgegevens:</h4>
                    <div className="space-y-1 text-sm text-blue-800">
                      <p><strong>Naam:</strong> {customerData.customer_name}</p>
                      {customerData.customer_email && <p><strong>Email:</strong> {customerData.customer_email}</p>}
                      {customerData.customer_phone && <p><strong>Telefoon:</strong> {customerData.customer_phone}</p>}
                      {customerData.customer_address && <p><strong>Adres:</strong> {customerData.customer_address}</p>}
                      {customerData.customer_city && <p><strong>Plaats:</strong> {customerData.customer_city}</p>}
                      {customerData.customer_postal_code && <p><strong>Postcode:</strong> {customerData.customer_postal_code}</p>}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold text-blue-900 mb-3">Kosten breakdown:</h4>
                    <div className="space-y-1 text-sm text-blue-800">
                      <div className="flex justify-between">
                        <span>Apparatuur per maand:</span>
                        <span>€{calculations.monthly_equipment_cost.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Meldkamer per maand:</span>
                        <span>€{calculations.monthly_monitoring_cost.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Onderhoud per maand:</span>
                        <span>€{calculations.monthly_maintenance_cost.toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Customer confirmation */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center mb-4">
            <FaUser className="text-blue-600 text-xl mr-3" />
            <h3 className="text-xl font-semibold text-gray-900">Klantgegevens Bevestiging</h3>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Naam:</strong> {customerData.customer_name}
              </div>
              {customerData.customer_email && (
                <div>
                  <strong>Email:</strong> {customerData.customer_email}
                </div>
              )}
              {customerData.customer_phone && (
                <div>
                  <strong>Telefoon:</strong> {customerData.customer_phone}
                </div>
              )}
              {customerData.customer_address && (
                <div>
                  <strong>Adres:</strong> {customerData.customer_address}
                </div>
              )}
              {customerData.customer_city && (
                <div>
                  <strong>Plaats:</strong> {customerData.customer_city}
                </div>
              )}
              {customerData.customer_postal_code && (
                <div>
                  <strong>Postcode:</strong> {customerData.customer_postal_code}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Signature section */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border p-6">
            <h3 className="font-semibold text-gray-900 mb-6 flex items-center text-lg">
              <FaSignature className="mr-3 text-blue-600" />
              Digitale Handtekening *
            </h3>
            
            {!signatureData ? (
              <div className="text-center py-8">
                <FaSignature className="mx-auto text-4xl text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">Plaats uw handtekening om de offerte te bevestigen</p>
                <button
                  type="button"
                  onClick={() => setShowSignature(true)}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  Handtekening plaatsen
                </button>
              </div>
            ) : (
              <div className="text-center">
                <div className="mb-4 inline-block border-2 border-gray-200 rounded-lg p-4 bg-gray-50">
                  <img 
                    src={signatureData} 
                    alt="Handtekening" 
                    className="max-w-full h-32 object-contain"
                  />
                </div>
                <div>
                  <button
                    type="button"
                    onClick={() => {
                      setSignatureData('');
                      setShowSignature(true);
                    }}
                    className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors mr-3"
                  >
                    Handtekening wijzigen
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Submit button */}
          <div className="flex justify-between items-center">
            <button
              type="button"
              onClick={() => navigate(-1)}
              className="text-gray-600 hover:text-gray-800 transition-colors font-medium"
            >
              ← Terug naar configuratie
            </button>
            
            <button
              type="submit"
              disabled={isSubmitting || !signatureData}
              className="bg-green-600 text-white px-8 py-3 rounded-xl hover:bg-green-700 transition-colors font-medium disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center shadow-lg"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Bezig met opslaan...
                </>
              ) : (
                <>
                  <FaCheck className="mr-2" />
                  Offerte ondertekenen en opslaan
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Signature modal */}
      {showSignature && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl p-6 max-w-lg w-full shadow-2xl">
            <h3 className="font-semibold text-gray-900 mb-4 text-lg">Plaats uw handtekening</h3>
            
            <div className="border-2 border-gray-300 rounded-lg mb-4 bg-gray-50">
              <SignatureCanvas
                ref={signatureRef}
                canvasProps={{
                  width: 400,
                  height: 200,
                  className: 'signature-canvas w-full rounded-lg'
                }}
              />
            </div>
            
            <div className="flex justify-between">
              <button
                type="button"
                onClick={clearSignature}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center"
              >
                <FaEraser className="mr-2" />
                Wissen
              </button>
              
              <div className="space-x-2">
                <button
                  type="button"
                  onClick={() => setShowSignature(false)}
                  className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Annuleren
                </button>
                <button
                  type="button"
                  onClick={saveSignature}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                >
                  Opslaan
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </MobileContainer>
  );
};

export default SimpleQuoteSignaturePage;
