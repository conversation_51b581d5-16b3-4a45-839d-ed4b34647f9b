/**
 * Simple Quote Service
 * Handles API calls for the simple quote system
 */

import api from '../api';

export interface Product {
  name: string;
  description: string;
  price_excl_vat: number;
  price_incl_vat: number;
  image: string;
}

export interface QuoteCalculation {
  base_cost_excl_vat: number;
  extra_cost_excl_vat: number;
  total_product_cost_excl_vat: number;
  extra_items_count: number;
  installation_cost_before_discount: number;
  discount_percentage: number;
  discount_amount: number;
  final_installation_cost: number;
  monthly_equipment_cost: number;
  monthly_monitoring_cost: number;
  monthly_maintenance_cost: number;
  total_monthly_cost: number;
  max_discount_allowed: number;
}

export interface SimpleQuote {
  id: number;
  quote_number: string;
  customer_id?: number;
  created_by: number;
  category: string;
  payment_terms: string;
  discount_percentage: number;
  customer_name: string;
  customer_email?: string;
  customer_phone?: string;
  customer_address?: string;
  customer_city?: string;
  customer_postal_code?: string;
  extra_magneetcontact: number;
  extra_shock_sensor: number;
  extra_pir_normaal: number;
  extra_rookmelder: number;
  extra_pircam: number;
  extra_bediendeel: number;
  extra_sirene: number;
  videodoorbell_free: boolean;
  videodoorbell_paid: boolean;
  base_installation_cost: number;
  total_product_cost_excl_vat: number;
  total_installation_cost: number;
  monthly_equipment_cost: number;
  monthly_monitoring_cost: number;
  monthly_maintenance_cost: number;
  status: string;
  signature_data?: string;
  signed_at?: string;
  created_at: string;
  updated_at: string;
  document_id?: number;
}

export interface CreateQuoteData {
  category: string;
  payment_terms: string;
  discount_percentage: number;
  customer_name: string;
  customer_email?: string;
  customer_phone?: string;
  customer_address?: string;
  customer_city?: string;
  customer_postal_code?: string;
  extra_magneetcontact: number;
  extra_shock_sensor: number;
  extra_pir_normaal: number;
  extra_rookmelder: number;
  extra_pircam: number;
  extra_bediendeel: number;
  extra_sirene: number;
  videodoorbell_free: boolean;
  videodoorbell_paid: boolean;
}

class SimpleQuoteService {
  private baseUrl = '/api/simple-quotes';

  /**
   * Get all available products for a category
   */
  async getProducts(category?: string): Promise<{
    products: Record<string, any>;
    pricing_config: any;
    discount_limits: any;
  }> {
    const url = category ? `/simple-quotes/products?category=${category}` : `/simple-quotes/products`;
    const response = await api.get(url);
    return response.data;
  }

  /**
   * Get specific product information
   */
  async getProduct(productKey: string): Promise<{ product: Product }> {
    const response = await api.get(`/simple-quotes/products/${productKey}`);
    return response.data;
  }

  /**
   * Calculate quote totals
   */
  async calculateQuote(data: {
    extra_products: Record<string, number>;
    payment_terms: string;
    discount_percentage: number;
    videodoorbell_free: boolean;
    videodoorbell_paid: boolean;
  }): Promise<{ calculations: QuoteCalculation }> {
    const response = await api.post('/simple-quotes/calculate', data);
    return response.data;
  }

  /**
   * Get available categories
   */
  async getCategories(): Promise<{ categories: Record<string, any> }> {
    const response = await api.get('/simple-quotes/categories');
    return response.data;
  }

  /**
   * Create a new quote
   */
  async createQuote(data: CreateQuoteData): Promise<{ quote: SimpleQuote }> {
    try {
      const response = await api.post('/simple-quotes/quotes', data);
      return response.data;
    } catch (error: any) {
      console.error('Error creating quote:', error);
      // Re-throw with more specific error message
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to create quote. Please try again.');
    }
  }

  /**
   * Get all quotes with pagination
   */
  async getAllQuotes(page = 1, perPage = 20): Promise<{
    quotes: SimpleQuote[];
    total: number;
    page: number;
    per_page: number;
  }> {
    const response = await api.get(`/simple-quotes/quotes?page=${page}&per_page=${perPage}`);
    return response.data;
  }

  /**
   * Get a quote by ID
   */
  async getQuote(quoteId: number): Promise<{ quote: SimpleQuote }> {
    const response = await api.get(`/simple-quotes/quotes/${quoteId}`);
    return response.data;
  }

  /**
   * Update a quote
   */
  async updateQuote(quoteId: number, data: Partial<CreateQuoteData>): Promise<{ quote: SimpleQuote }> {
    const response = await api.put(`/simple-quotes/quotes/${quoteId}`, data);
    return response.data;
  }

  /**
   * Sign a quote
   */
  async signQuote(quoteId: number, signatureData: string): Promise<{ quote: SimpleQuote }> {
    try {
      const response = await api.post(`/simple-quotes/quotes/${quoteId}/sign`, {
        signature_data: signatureData
      });
      return response.data;
    } catch (error: any) {
      console.error('Error signing quote:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to sign quote. Please try again.');
    }
  }

  /**
   * Download quote as PDF
   */
  async downloadQuotePDF(quoteId: number): Promise<Blob> {
    try {
      const response = await api.get(`/simple-quotes/quotes/${quoteId}/pdf`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error: any) {
      console.error('Error downloading PDF:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to download PDF. Please try again.');
    }
  }

  /**
   * Get product image URL
   */
  getProductImageUrl(filename: string): string {
    return `${this.baseUrl}/product-images/${filename}`;
  }
}

// Export singleton instance
export const simpleQuoteService = new SimpleQuoteService();
export default simpleQuoteService;
