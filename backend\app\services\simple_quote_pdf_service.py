"""
Simple Quote PDF generation service.
This module handles PDF generation for simple quotes.
"""
import logging
from typing import Dict, Optional
from datetime import datetime, timedelta
import os
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT
import base64
import io
from PIL import Image as PILImage

from app.models.simple_quote import SimpleQuote
from app.utils.simple_quote_products import BASE_ALARM_PACKAGE, EXTRA_PRODUCTS, get_product_by_key

# Configure logging
logger = logging.getLogger(__name__)

class SimpleQuotePDFService:
    """Service for generating PDF documents from simple quotes."""

    def __init__(self):
        """Initialize the PDF service."""
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()

    def _setup_custom_styles(self):
        """Setup custom paragraph styles."""
        # Company header style
        self.styles.add(ParagraphStyle(
            name='CompanyHeader',
            parent=self.styles['Heading1'],
            fontSize=24,
            textColor=colors.HexColor('#1f2937'),
            alignment=TA_CENTER,
            spaceAfter=20
        ))

        # Quote title style
        self.styles.add(ParagraphStyle(
            name='QuoteTitle',
            parent=self.styles['Heading2'],
            fontSize=18,
            textColor=colors.HexColor('#3b82f6'),
            alignment=TA_CENTER,
            spaceAfter=15
        ))

        # Section header style
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading3'],
            fontSize=14,
            textColor=colors.HexColor('#1f2937'),
            spaceBefore=15,
            spaceAfter=10
        ))

        # Customer info style
        self.styles.add(ParagraphStyle(
            name='CustomerInfo',
            parent=self.styles['Normal'],
            fontSize=11,
            textColor=colors.HexColor('#374151'),
            leftIndent=20
        ))

        # Footer style
        self.styles.add(ParagraphStyle(
            name='Footer',
            parent=self.styles['Normal'],
            fontSize=9,
            textColor=colors.HexColor('#6b7280'),
            alignment=TA_CENTER
        ))

    def generate_quote_pdf(self, quote: SimpleQuote) -> bytes:
        """
        Generate PDF for a simple quote.

        Args:
            quote: SimpleQuote instance

        Returns:
            PDF content as bytes
        """
        try:
            # Create PDF buffer
            buffer = io.BytesIO()
            
            # Create document
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )

            # Build content
            story = []
            
            # Add company header
            story.extend(self._add_company_header())

            # Add quote header
            story.extend(self._add_quote_header(quote))
            
            # Add customer information
            story.extend(self._add_customer_info(quote))
            
            # Add quote details
            story.extend(self._add_quote_details(quote))
            
            # Add product list
            story.extend(self._add_product_list(quote))
            
            # Add pricing summary
            story.extend(self._add_pricing_summary(quote))
            
            # Add signature section
            story.extend(self._add_signature_section(quote))
            
            # Add terms and conditions
            story.extend(self._add_terms_and_conditions())
            
            # Add footer
            story.extend(self._add_footer())

            # Build PDF
            doc.build(story)
            
            # Get PDF content
            pdf_content = buffer.getvalue()
            buffer.close()
            
            logger.info(f"Generated PDF for quote {quote.quote_number}")
            return pdf_content

        except Exception as e:
            logger.error(f"Failed to generate PDF for quote {quote.id}: {str(e)}")
            raise Exception(f"Failed to generate PDF: {str(e)}")

    def _add_company_header(self) -> list:
        """Add company header to PDF with logo and experience badges."""
        content = []

        # Create header table with logo, company info, and experience badges
        try:
            logo_path = os.path.join('app', 'static', 'images', 'store_information', 'winkellogo.png')
            experience_path = os.path.join('app', 'static', 'images', 'store_information', 'ervaring.png')
            bewaking_path = os.path.join('app', 'static', 'images', 'store_information', '24uurperdagbewaking.png')

            # Create header table data
            header_data = []

            # Left column - Logo (if exists)
            left_cell = ""
            if os.path.exists(logo_path):
                logo = Image(logo_path, width=3*cm, height=1.5*cm)
                left_cell = logo

            # Center column - Company info
            center_cell = """
            <para align="center">
            <font size="20"><b>De SecurityWinkel</b></font><br/>
            <font size="10">Professionele beveiligingsoplossingen<br/>
            Tel: ************<br/>
            Technische vragen: <EMAIL><br/>
            Vragen over uw bestelling: <EMAIL></font>
            </para>
            """

            # Right column - Experience badges (smaller)
            right_cell_content = []
            if os.path.exists(experience_path):
                exp_img = Image(experience_path, width=2.5*cm, height=1.2*cm)
                right_cell_content.append(exp_img)
            if os.path.exists(bewaking_path):
                bewaking_img = Image(bewaking_path, width=2.5*cm, height=1.2*cm)
                right_cell_content.append(bewaking_img)

            # Create the header table
            if right_cell_content:
                # Create a mini table for the right cell images
                right_mini_table = Table([right_cell_content], colWidths=[2.5*cm] * len(right_cell_content))
                right_mini_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('LEFTPADDING', (0, 0), (-1, -1), 2),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 2),
                ]))
                header_data = [[left_cell, Paragraph(center_cell, self.styles['Normal']), right_mini_table]]
            else:
                header_data = [[left_cell, Paragraph(center_cell, self.styles['Normal']), ""]]

            # Create main header table
            header_table = Table(header_data, colWidths=[4*cm, 8*cm, 5*cm])
            header_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (0, 0), 'LEFT'),    # Logo left
                ('ALIGN', (1, 0), (1, 0), 'CENTER'),  # Company info center
                ('ALIGN', (2, 0), (2, 0), 'RIGHT'),   # Badges right
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('LEFTPADDING', (0, 0), (-1, -1), 5),
                ('RIGHTPADDING', (0, 0), (-1, -1), 5),
                ('TOPPADDING', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ]))

            content.append(header_table)

        except Exception as e:
            logger.warning(f"Failed to create header table: {str(e)}")
            # Fallback to simple header
            content.append(Paragraph("De SecurityWinkel", self.styles['CompanyHeader']))
            company_info = """
            <para align="center">
            Professionele beveiligingsoplossingen<br/>
            Tel: ************<br/>
            Technische vragen: <EMAIL><br/>
            Vragen over uw bestelling: <EMAIL>
            </para>
            """
            content.append(Paragraph(company_info, self.styles['Normal']))

        content.append(Spacer(1, 15))
        content.append(HRFlowable(width="100%", thickness=2, color=colors.HexColor('#3b82f6')))
        content.append(Spacer(1, 20))

        return content

    def _add_quote_header(self, quote: SimpleQuote) -> list:
        """Add quote header information."""
        content = []
        
        content.append(Paragraph("OFFERTE", self.styles['QuoteTitle']))
        
        # Quote details table
        quote_data = [
            ['Offertenummer:', quote.quote_number or 'Concept'],
            ['Datum:', quote.created_at.strftime('%d-%m-%Y') if quote.created_at else datetime.now().strftime('%d-%m-%Y')],
            ['Geldig tot:', (quote.created_at + timedelta(days=30)).strftime('%d-%m-%Y') if quote.created_at else (datetime.now() + timedelta(days=30)).strftime('%d-%m-%Y')],
            ['Status:', 'Ondertekend' if quote.status == 'SIGNED' else 'Concept']
        ]
        
        quote_table = Table(quote_data, colWidths=[4*cm, 6*cm])
        quote_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 0),
            ('TOPPADDING', (0, 0), (-1, -1), 4),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 4),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#E5E7EB')),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#F9FAFB')),
        ]))
        
        content.append(quote_table)
        content.append(Spacer(1, 20))
        
        return content

    def _add_customer_info(self, quote: SimpleQuote) -> list:
        """Add customer information in a professional table format."""
        content = []

        content.append(Paragraph("Klantgegevens", self.styles['SectionHeader']))

        # Create customer info table
        customer_data = []

        # Always add name
        customer_data.append(['Naam:', quote.customer_name or 'Niet opgegeven'])

        # Add other fields if they exist
        if quote.customer_email:
            customer_data.append(['Email:', quote.customer_email])
        if quote.customer_phone:
            customer_data.append(['Telefoon:', quote.customer_phone])
        if quote.customer_address:
            customer_data.append(['Adres:', quote.customer_address])
        if quote.customer_city:
            customer_data.append(['Plaats:', quote.customer_city])
        if quote.customer_postal_code:
            customer_data.append(['Postcode:', quote.customer_postal_code])

        # Create table
        customer_table = Table(customer_data, colWidths=[3*cm, 10*cm])
        customer_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 0),
            ('TOPPADDING', (0, 0), (-1, -1), 3),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#E5E7EB')),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#F9FAFB')),
        ]))

        content.append(customer_table)
        content.append(Spacer(1, 20))

        return content

    def _add_quote_details(self, quote: SimpleQuote) -> list:
        """Add quote details and category."""
        content = []
        
        content.append(Paragraph("Offerte Details", self.styles['SectionHeader']))
        
        category_name = {
            'ALARM': 'Alarmsysteem',
            'CAMERAS': 'Camera systeem',
            'ALARM_CAMERAS': 'Alarm + Camera systeem'
        }.get(quote.category, quote.category)

        payment_terms = {
            'ONE_TERM': '1 termijn',
            'TWO_TERMS': '2 termijnen',
            'THREE_TERMS': '3 termijnen'
        }.get(quote.payment_terms, quote.payment_terms)
        
        details_info = f"""
        <para>
        <b>Systeem type:</b> {category_name}<br/>
        <b>Betalingsvoorwaarden:</b> {payment_terms}<br/>
        <b>Korting:</b> {quote.discount_percentage}%<br/>
        </para>
        """
        
        content.append(Paragraph(details_info, self.styles['CustomerInfo']))
        content.append(Spacer(1, 15))
        
        return content

    def _add_product_list(self, quote: SimpleQuote) -> list:
        """Add product list to PDF with images in table format."""
        content = []

        content.append(Paragraph("Producten & Diensten", self.styles['SectionHeader']))

        # Create product table volgens AMSPM layout - 4 columns
        table_data = [['ARTIKELNAAM', 'HOEVEELHEID', 'PRIJS PER STUK', 'PLAATJES']]

        # Add base package if alarm category (VERPLICHT PAKKET)
        if quote.category in ['ALARM', 'ALARM_CAMERAS']:
            # Add base package products met juiste namen
            for product_key, product in BASE_ALARM_PACKAGE.items():
                # Get product image
                image_cell = self._get_product_image_cell(product['image'])

                # Gebruik de EXACTE namen uit de configuratie
                table_data.append([
                    f"{product['name']}\n{product['description']}",
                    str(product['quantity']),
                    f"€{product['price_excl_vat']:.2f}",
                    image_cell
                ])

        # Add extra products
        extra_products = {
            'extra_magneetcontact': quote.extra_magneetcontact,
            'extra_shock_sensor': quote.extra_shock_sensor,
            'extra_pir_normaal': quote.extra_pir_normaal,
            'extra_rookmelder': quote.extra_rookmelder,
            'extra_pircam': quote.extra_pircam,
            'extra_bediendeel': quote.extra_bediendeel,
            'extra_sirene': quote.extra_sirene
        }

        for product_key, quantity in extra_products.items():
            if quantity > 0:
                product_info = get_product_by_key(product_key.replace('extra_', ''))
                if product_info:
                    image_cell = self._get_product_image_cell(product_info['image'])

                    table_data.append([
                        f"{product_info['name']}\n{product_info['description'][:30]}...",
                        str(quantity),
                        f"€{product_info['price_incl_vat']:.2f}",
                        image_cell
                    ])

        # Add videodoorbell
        if quote.videodoorbell_free:
            videodoorbell_info = get_product_by_key('videodoorbell')
            if videodoorbell_info:
                image_cell = self._get_product_image_cell(videodoorbell_info['image'])
                table_data.append([
                    'Video Deurbel (GRATIS)\nSlimme video deurbel',
                    '1',
                    '€0,00',
                    image_cell
                ])
        elif quote.videodoorbell_paid:
            videodoorbell_info = get_product_by_key('videodoorbell')
            if videodoorbell_info:
                image_cell = self._get_product_image_cell(videodoorbell_info['image'])
                table_data.append([
                    'Video Deurbel\nSlimme video deurbel',
                    '1',
                    '€249,99',
                    image_cell
                ])

        # Create table with proper sizing
        product_table = Table(table_data, colWidths=[5*cm, 2.5*cm, 3*cm, 4*cm])
        product_table.setStyle(TableStyle([
            # Header styling
            ('BACKGROUND', (0, 0), (-1, 0), colors.white),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),

            # Data rows styling
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),      # Product names left
            ('ALIGN', (1, 1), (1, -1), 'CENTER'),    # Quantity center
            ('ALIGN', (2, 1), (2, -1), 'CENTER'),    # Price center
            ('ALIGN', (3, 1), (3, -1), 'CENTER'),    # Images center
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

            # Grid and borders
            ('GRID', (0, 0), (-1, -1), 2, colors.black),
            ('LINEBELOW', (0, 0), (-1, 0), 2, colors.black),

            # Row height
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#f8f9fa')]),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))

        content.append(product_table)
        content.append(Spacer(1, 20))

        return content

    def _get_product_image_cell(self, image_path: str):
        """Get product image for table cell."""
        try:
            # Construct full path to image
            full_path = os.path.join('app', 'static', 'product_images', image_path)

            if os.path.exists(full_path):
                # Create image with fixed size for table
                img = Image(full_path, width=2.5*cm, height=2*cm)
                return img
            else:
                # Return placeholder text if image not found
                return "PLAATJE"

        except Exception as e:
            logger.warning(f"Failed to load product image {image_path}: {str(e)}")
            return "PLAATJE"

    def _add_pricing_summary(self, quote: SimpleQuote) -> list:
        """Add pricing summary with services section."""
        content = []

        # Services section with checkmarks
        services_data = [
            ['MELDKAMER', '✓'],
            ['SERVICE', '✓'],
            ['ONDERHOUD', '✓']
        ]

        services_table = Table(services_data, colWidths=[12*cm, 2*cm])
        services_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.white),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 2, colors.black),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))

        content.append(services_table)
        content.append(Spacer(1, 10))

        # Calculate final prices
        final_installation_cost = quote.get_final_installation_cost()
        total_monthly = quote.get_total_monthly_cost()

        # Installation costs section - LEASE SYSTEEM
        discount_text = f"Korting: {quote.discount_percentage}% (Max: {quote.get_max_discount()}%)"
        final_cost_text = f"€{final_installation_cost:.2f}"

        installation_data = [
            ['EENMALIGE INSTALLATIEKOSTEN', final_cost_text, 'KORTING SLIDER BALK'],
            [f'Basis: €{quote.total_installation_cost:.2f}', '', discount_text]
        ]

        installation_table = Table(installation_data, colWidths=[8*cm, 3*cm, 3*cm])
        installation_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('BACKGROUND', (0, 1), (-1, 1), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, 1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('FONTSIZE', (0, 1), (-1, 1), 10),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),
            ('ALIGN', (2, 0), (2, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 2, colors.black),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))

        content.append(installation_table)
        content.append(Spacer(1, 10))

        # Monthly costs section - LEASE SYSTEEM
        monthly_breakdown = [
            f"Apparatuur: €{quote.monthly_equipment_cost:.2f}",
            f"Meldkamer: €{quote.monthly_monitoring_cost:.2f}",
            f"Onderhoud: €{quote.monthly_maintenance_cost:.2f}"
        ]

        monthly_data = [
            ['MAANDELIJKSE KOSTEN', f"€{total_monthly:.2f}"],
            [' + '.join(monthly_breakdown), 'MINIMUM €49,99']
        ]

        monthly_table = Table(monthly_data, colWidths=[10*cm, 4*cm])
        monthly_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('BACKGROUND', (0, 1), (-1, 1), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, 1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('FONTSIZE', (0, 1), (-1, 1), 9),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),
            ('GRID', (0, 0), (-1, -1), 2, colors.black),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))

        content.append(monthly_table)
        content.append(Spacer(1, 20))

        return content

    def _add_signature_section(self, quote: SimpleQuote) -> list:
        """Add signature section with customer details."""
        content = []

        # Customer details and signature section - AMSPM Layout
        signature_data = [
            ['VOORNAAM', quote.customer_name.split()[0] if quote.customer_name else '', 'HANDTEKENING'],
            ['ACHTERNAAM', ' '.join(quote.customer_name.split()[1:]) if len(quote.customer_name.split()) > 1 else '', ''],
            ['DATUM', quote.signed_at.strftime('%d-%m-%Y') if quote.signed_at else '', ''],
            ['PLAATS', quote.customer_city or '', '']
        ]

        # If quote is signed, add the signature image
        signature_cell = ''
        if quote.signature_data and quote.status == 'SIGNED':
            try:
                # Decode base64 signature
                signature_data_clean = quote.signature_data.split(',')[1] if ',' in quote.signature_data else quote.signature_data
                signature_bytes = base64.b64decode(signature_data_clean)

                # Create image from signature
                signature_image = PILImage.open(io.BytesIO(signature_bytes))

                # Save to temporary buffer
                img_buffer = io.BytesIO()
                signature_image.save(img_buffer, format='PNG')
                img_buffer.seek(0)

                # Add signature image
                signature_cell = Image(img_buffer, width=4*cm, height=2*cm)

            except Exception as e:
                logger.warning(f"Failed to add signature image: {str(e)}")
                signature_cell = 'Digitaal ondertekend'

        # Update signature data with actual signature
        signature_data[0][2] = signature_cell if signature_cell else ''

        # Fill in customer data if available
        if quote.customer_name:
            name_parts = quote.customer_name.split(' ', 1)
            signature_data[0][1] = name_parts[0] if name_parts else ''
            signature_data[1][1] = name_parts[1] if len(name_parts) > 1 else ''

        if quote.signed_at:
            signature_data[2][1] = quote.signed_at.strftime('%d-%m-%Y')

        if quote.customer_city:
            signature_data[3][1] = quote.customer_city

        signature_table = Table(signature_data, colWidths=[3*cm, 6*cm, 5*cm])
        signature_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.white),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (0, -1), 'LEFT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('ALIGN', (2, 0), (2, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 2, colors.black),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            # Make signature cell span multiple rows visually
            ('SPAN', (2, 0), (2, -1)),
        ]))

        content.append(signature_table)
        content.append(Spacer(1, 20))

        return content

    def _add_terms_and_conditions(self) -> list:
        """Add terms and conditions."""
        content = []

        content.append(Paragraph("Algemene Voorwaarden", self.styles['SectionHeader']))

        terms = """
        <para fontSize="9">
        1. Deze offerte is 30 dagen geldig vanaf de datum van uitgifte.<br/>
        2. Alle prijzen zijn inclusief BTW en exclusief onvoorziene werkzaamheden.<br/>
        3. Betaling dient te geschieden volgens de overeengekomen betalingsvoorwaarden.<br/>
        4. Installatie wordt uitgevoerd door gecertificeerde technici.<br/>
        5. Op alle apparatuur geldt fabrieksgarantie, installatie heeft 2 jaar garantie.<br/>
        6. Wijzigingen in de offerte zijn mogelijk tot de installatiedatum.<br/>
        7. Bij annulering binnen 14 dagen na ondertekening worden geen kosten in rekening gebracht.<br/>
        8. Voor deze overeenkomst gelden de algemene voorwaarden van De SecurityWinkel.<br/>
        </para>
        """

        content.append(Paragraph(terms, self.styles['Normal']))
        content.append(Spacer(1, 20))

        return content

    def _add_footer(self) -> list:
        """Add footer information."""
        content = []

        footer_text = """
        <para align="center">
        © 2025, De SecurityWinkel<br/>
        Produktieweg 1, 6045 JC Roermond<br/>
        Tel: ************<br/>
        Technische vragen: <EMAIL> | Vragen over uw bestelling: <EMAIL>
        </para>
        """

        content.append(HRFlowable(width="100%", thickness=1, color=colors.HexColor('#e5e7eb')))
        content.append(Spacer(1, 10))
        content.append(Paragraph(footer_text, self.styles['Footer']))

        return content
