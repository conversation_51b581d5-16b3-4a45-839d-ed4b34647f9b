import React, { useState, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaArrowLeft, FaSignature, FaEraser, FaCheck } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import { MobileContainer, MobilePageHeader } from '../components/common/MobileUtils';
import SignatureCanvas from 'react-signature-canvas';
import simpleOfferteService from '../services/simpleOfferteService';

interface CustomerFormData {
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  customer_address: string;
  customer_city: string;
  customer_postal_code: string;
}

const SimpleQuoteCustomerPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const signatureRef = useRef<SignatureCanvas>(null);
  
  // Get quote configuration from previous page
  const quoteConfig = location.state?.quoteConfig;
  
  const [customerData, setCustomerData] = useState<CustomerFormData>({
    customer_name: '',
    customer_email: '',
    customer_phone: '',
    customer_address: '',
    customer_city: '',
    customer_postal_code: ''
  });
  
  const [signatureData, setSignatureData] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSignature, setShowSignature] = useState(false);

  // Redirect if no quote config
  if (!quoteConfig) {
    navigate('/simple-quotes');
    return null;
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCustomerData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const clearSignature = () => {
    if (signatureRef.current) {
      signatureRef.current.clear();
      setSignatureData('');
    }
  };

  const saveSignature = () => {
    if (signatureRef.current) {
      const signature = signatureRef.current.toDataURL();
      setSignatureData(signature);
      setShowSignature(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!signatureData) {
      alert('Handtekening is verplicht');
      return;
    }

    setIsSubmitting(true);

    try {
      // Create the offerte with signature
      const offerteData = {
        ...customerData,
        category: quoteConfig.category.toUpperCase(),
        payment_terms: quoteConfig.paymentTerms.toUpperCase(),
        discount_percentage: quoteConfig.discountPercentage,
        videodoorbell_free: quoteConfig.videodoorbellFree,
        videodoorbell_paid: quoteConfig.videodoorbellPaid,
        signature_data: signatureData,
        ...quoteConfig.extraProducts
      };

      const result = await simpleOfferteService.createOfferte(offerteData);

      // Navigate to success page
      navigate('/simple-quotes/success', {
        state: {
          customerId: result.customer.id,
          customerName: result.customer.name,
          documentId: result.document.id,
          documentName: result.document.name,
          quoteNumber: result.quote_number || result.document.name
        }
      });

    } catch (error) {
      console.error('Error submitting quote:', error);
      alert('Er is een fout opgetreden bij het opslaan van de offerte');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getCategoryTitle = () => {
    switch (quoteConfig.category) {
      case 'alarm': return 'Alarm Systeem';
      case 'cameras': return 'Camera Systeem';
      case 'alarm_cameras': return 'Alarm + Camera Systeem';
      default: return 'Offerte';
    }
  };

  return (
    <MobileContainer>
      <Breadcrumbs />
      
      <MobilePageHeader
        title={`Klantgegevens: ${getCategoryTitle()}`}
        subtitle="Vul uw gegevens in en onderteken de offerte"
      />

      <div className="space-y-6">
        {/* Back button */}
        <div className="flex items-center">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center text-gray-600 hover:text-gray-800 transition-colors"
          >
            <FaArrowLeft className="mr-2" />
            Terug naar configuratie
          </button>
        </div>

        {/* Quote summary */}
        {quoteConfig.calculations && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">Offerte Samenvatting</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-blue-700">Installatiekosten:</span>
                <div className="font-bold text-lg">€{quoteConfig.calculations.final_installation_cost.toFixed(2)}</div>
              </div>
              <div>
                <span className="text-blue-700">Maandelijks:</span>
                <div className="font-bold text-lg">€{quoteConfig.calculations.total_monthly_cost.toFixed(2)}</div>
              </div>
            </div>
          </div>
        )}

        {/* Customer form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-white rounded-lg border p-4">
            <h3 className="font-semibold text-gray-900 mb-4">Klantgegevens</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Naam *
                </label>
                <input
                  type="text"
                  name="customer_name"
                  value={customerData.customer_name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  name="customer_email"
                  value={customerData.customer_email}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Telefoon
                </label>
                <input
                  type="tel"
                  name="customer_phone"
                  value={customerData.customer_phone}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Postcode
                </label>
                <input
                  type="text"
                  name="customer_postal_code"
                  value={customerData.customer_postal_code}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Plaats
                </label>
                <input
                  type="text"
                  name="customer_city"
                  value={customerData.customer_city}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Adres
                </label>
                <textarea
                  name="customer_address"
                  value={customerData.customer_address}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Signature section */}
          <div className="bg-white rounded-lg border p-4">
            <h3 className="font-semibold text-gray-900 mb-4 flex items-center">
              <FaSignature className="mr-2" />
              Digitale Handtekening *
            </h3>
            
            {!signatureData ? (
              <div>
                <button
                  type="button"
                  onClick={() => setShowSignature(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Handtekening plaatsen
                </button>
              </div>
            ) : (
              <div>
                <div className="mb-4">
                  <img 
                    src={signatureData} 
                    alt="Handtekening" 
                    className="border rounded-lg max-w-full h-32 object-contain"
                  />
                </div>
                <button
                  type="button"
                  onClick={() => {
                    setSignatureData('');
                    setShowSignature(true);
                  }}
                  className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Handtekening wijzigen
                </button>
              </div>
            )}
          </div>

          {/* Submit button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting || !signatureData}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Bezig met opslaan...
                </>
              ) : (
                <>
                  <FaCheck className="mr-2" />
                  Offerte ondertekenen en opslaan
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Signature modal */}
      {showSignature && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full">
            <h3 className="font-semibold text-gray-900 mb-4">Plaats uw handtekening</h3>
            
            <div className="border-2 border-gray-300 rounded-lg mb-4">
              <SignatureCanvas
                ref={signatureRef}
                canvasProps={{
                  width: 400,
                  height: 200,
                  className: 'signature-canvas w-full'
                }}
              />
            </div>
            
            <div className="flex justify-between">
              <button
                type="button"
                onClick={clearSignature}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center"
              >
                <FaEraser className="mr-2" />
                Wissen
              </button>
              
              <div className="space-x-2">
                <button
                  type="button"
                  onClick={() => setShowSignature(false)}
                  className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Annuleren
                </button>
                <button
                  type="button"
                  onClick={saveSignature}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                >
                  Opslaan
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </MobileContainer>
  );
};

export default SimpleQuoteCustomerPage;
