-- <PERSON><PERSON>t to fix Simple Quotes enum constraints in PostgreSQL
-- Run this in pgAdmin to fix the enum issues

-- First, let's check the current table structure
-- SELECT column_name, data_type, column_default, is_nullable 
-- FROM information_schema.columns 
-- WHERE table_name = 'simple_quotes';

-- Drop existing enum constraints if they exist
ALTER TABLE simple_quotes DROP CONSTRAINT IF EXISTS simple_quotes_category_check;
ALTER TABLE simple_quotes DROP CONSTRAINT IF EXISTS simple_quotes_payment_terms_check;
ALTER TABLE simple_quotes DROP CONSTRAINT IF EXISTS simple_quotes_status_check;

-- Drop existing enum types if they exist
DROP TYPE IF EXISTS quotecategory CASCADE;
DROP TYPE IF EXISTS paymentterms CASCADE;
DROP TYPE IF EXISTS quotestatus CASCADE;

-- Create new enum types with correct values
CREATE TYPE quotecategory AS ENUM ('ALARM', 'CAMERAS', 'ALARM_CAMERAS');
CREATE TYPE paymentterms AS ENUM ('ONE_TERM', 'TWO_TERMS', 'THREE_TERMS');
CREATE TYPE quotestatus AS ENUM ('DRAFT', 'SENT', 'SIGNED', 'CANCELLED');

-- Update the table columns to use the new enum types
-- First, we need to handle existing data if any
UPDATE simple_quotes SET category = 'ALARM' WHERE category = 'alarm';
UPDATE simple_quotes SET category = 'CAMERAS' WHERE category = 'cameras';
UPDATE simple_quotes SET category = 'ALARM_CAMERAS' WHERE category = 'alarm_cameras';

UPDATE simple_quotes SET payment_terms = 'ONE_TERM' WHERE payment_terms = '1_term';
UPDATE simple_quotes SET payment_terms = 'TWO_TERMS' WHERE payment_terms = '2_terms';
UPDATE simple_quotes SET payment_terms = 'THREE_TERMS' WHERE payment_terms = '3_terms';

UPDATE simple_quotes SET status = 'DRAFT' WHERE status = 'draft';
UPDATE simple_quotes SET status = 'SENT' WHERE status = 'sent';
UPDATE simple_quotes SET status = 'SIGNED' WHERE status = 'signed';
UPDATE simple_quotes SET status = 'CANCELLED' WHERE status = 'cancelled';

-- Alter the columns to use the new enum types
ALTER TABLE simple_quotes
ALTER COLUMN category TYPE quotecategory USING category::quotecategory;

ALTER TABLE simple_quotes
ALTER COLUMN payment_terms TYPE paymentterms USING payment_terms::paymentterms;

ALTER TABLE simple_quotes
ALTER COLUMN status TYPE quotestatus USING status::quotestatus;

-- Set default values
ALTER TABLE simple_quotes
ALTER COLUMN payment_terms SET DEFAULT 'ONE_TERM';

ALTER TABLE simple_quotes
ALTER COLUMN status SET DEFAULT 'DRAFT';

-- Add NOT NULL constraints if they don't exist
ALTER TABLE simple_quotes
ALTER COLUMN category SET NOT NULL;

ALTER TABLE simple_quotes
ALTER COLUMN payment_terms SET NOT NULL;

ALTER TABLE simple_quotes
ALTER COLUMN status SET NOT NULL;

-- Verify the changes
SELECT 
    column_name, 
    data_type, 
    udt_name,
    column_default, 
    is_nullable 
FROM information_schema.columns 
WHERE table_name = 'simple_quotes'
AND column_name IN ('category', 'payment_terms', 'status');

-- Show enum values
SELECT 
    t.typname AS enum_name,
    e.enumlabel AS enum_value
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname IN ('quotecategory', 'paymentterms', 'quotestatus')
ORDER BY t.typname, e.enumsortorder;

-- Test insert to verify it works
-- INSERT INTO simple_quotes (
--     created_by, 
--     category, 
--     payment_terms, 
--     customer_name,
--     discount_percentage
-- ) VALUES (
--     1, 
--     'ALARM', 
--     'ONE_TERM', 
--     'Test Customer',
--     0.0
-- );

COMMIT;

-- Success message
SELECT 'Simple quotes enum constraints have been fixed successfully!' as status;
