"""
<PERSON><PERSON><PERSON> to create placeholder images for the simple quote system.
"""
import os
from PIL import Image, ImageDraw, ImageFont

def create_placeholder_image(filename, text, size=(300, 200)):
    """Create a placeholder image with text."""
    # Create image with light gray background
    img = Image.new('RGB', size, color='#f0f0f0')
    draw = ImageDraw.Draw(img)
    
    # Try to use a font, fallback to default if not available
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    # Calculate text position (center)
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    # Draw text
    draw.text((x, y), text, fill='#666666', font=font)
    
    # Draw border
    draw.rectangle([0, 0, size[0]-1, size[1]-1], outline='#cccccc', width=2)
    
    return img

def main():
    """Create all placeholder images."""
    # Define all required images
    images = {
        'centrale_hub.jpg': 'Centrale Hub',
        'bediendeel.jpg': 'Bediendeel',
        'sirene.jpg': 'Sirene',
        'pircam.jpg': 'PIR Camera',
        'shock_sensor.jpg': 'Shock Sensor',
        'magneetcontact.jpg': 'Magneetcontact',
        'brandmelder.jpg': 'Brandmelder',
        'videodoorbell.jpg': 'Video Deurbel',
        'pir_normaal.jpg': 'PIR Sensor',
        'camera_indoor.jpg': 'Binnen Camera',
        'camera_outdoor.jpg': 'Buiten Camera',
        'nvr_4ch.jpg': 'NVR 4 Kanalen',
        'nvr_8ch.jpg': 'NVR 8 Kanalen'
    }
    
    # Create directory if it doesn't exist
    output_dir = 'app/static/product_images'
    os.makedirs(output_dir, exist_ok=True)
    
    # Create each placeholder image
    for filename, text in images.items():
        img = create_placeholder_image(filename, text)
        filepath = os.path.join(output_dir, filename)
        img.save(filepath, 'JPEG', quality=85)
        print(f"Created: {filepath}")
    
    print(f"\nCreated {len(images)} placeholder images in {output_dir}")

if __name__ == "__main__":
    main()
