#!/usr/bin/env python3

import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.simple_quote_pdf_service import SimpleQuotePDFService
from app.models.simple_quote import SimpleQuote

def test_professional_pdf():
    """Test the professional PDF generation."""
    
    # Create a test quote with realistic data
    quote = SimpleQuote(
        customer_name='<PERSON><PERSON>',
        customer_email='<EMAIL>',
        customer_phone='0681610919',
        customer_address='Willem Bayerstraat 24',
        customer_city='Roermond',
        customer_postal_code='6042 BK',
        category='ALARM',
        payment_terms='ONE_TERM',
        discount_percentage=0.0,
        extra_magneetcontact=0,
        extra_shock_sensor=0,
        extra_pir_normaal=0,
        extra_rookmelder=0,
        extra_pircam=0,
        extra_bediendeel=0,
        extra_sirene=0,
        videodoorbell_free=False,
        videodoorbell_paid=False,
        created_at=datetime.now()
    )

    # Calculate totals to populate the cost fields
    quote.calculate_totals()
    
    try:
        # Generate PDF
        print("Generating professional PDF...")
        pdf_service = SimpleQuotePDFService()
        pdf_bytes = pdf_service.generate_quote_pdf(quote)

        # Save to file
        output_file = 'test_quote_professional.pdf'
        with open(output_file, 'wb') as f:
            f.write(pdf_bytes)

        print(f"✅ Professional PDF generated successfully: {output_file}")
        print(f"📄 File size: {len(pdf_bytes)} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating PDF: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_professional_pdf()
