"""
Simple Offerte Controller - Uses existing document system
Creates customers and offerte documents without complex quote tables.
"""
from flask import Blueprint, request, jsonify
from app.services.simple_offerte_service import SimpleOfferteService
from app.utils.security import roles_required
from app.utils.rate_limit import rate_limit
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
simple_offerte_bp = Blueprint('simple_offerte', __name__)

# Initialize service
offerte_service = SimpleOfferteService()

@simple_offerte_bp.route("/create", methods=["POST"])
@roles_required("administrator", "verkoper")
@rate_limit("20/minute")
def create_offerte():
    """
    Create a new offerte document with customer.
    
    Expected data:
    {
        "customer_name": "<PERSON>",
        "customer_email": "<EMAIL>",
        "customer_phone": "123456789",
        "customer_address": "Street 123",
        "customer_city": "Amsterdam",
        "customer_postal_code": "1234AB",
        "category": "ALARM",
        "payment_terms": "ONE_TERM",
        "discount_percentage": 10,
        "videodoorbell_free": false,
        "videodoorbell_paid": true,
        "extra_magneetcontact": 2,
        "extra_shock_sensor": 1,
        "extra_pir_normaal": 0,
        "extra_rookmelder": 1,
        "extra_pircam": 0,
        "extra_bediendeel": 0,
        "extra_sirene": 0,
        "signature_data": "data:image/png;base64,..."
    }
    
    Returns:
        JSON: Created customer and document info.
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No data provided"}), 400
            
        # Validate required fields
        required_fields = ['customer_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Get current user from request (set by security decorator)
        current_user = request.current_user
        
        # Create offerte with customer
        result = offerte_service.create_offerte_with_customer(data, current_user.id)
        
        return jsonify({
            "success": True,
            "message": "Offerte created successfully",
            "customer": result['customer'],
            "document": result['document']
        }), 201
        
    except Exception as e:
        logger.error(f"Failed to create offerte: {str(e)}")
        return jsonify({"error": str(e)}), 500

@simple_offerte_bp.route("/calculate", methods=["POST"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def calculate_offerte():
    """
    Calculate offerte totals without creating anything.
    Same calculation logic as the original simple quotes.
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Extract product quantities with safe defaults
        extra_products = data.get('extra_products', {})
        payment_terms = data.get('payment_terms', '1_term')

        # Safe conversion of discount percentage
        try:
            discount_percentage = float(data.get('discount_percentage', 0))
        except (ValueError, TypeError):
            discount_percentage = 0.0

        videodoorbell_free = data.get('videodoorbell_free', False)
        videodoorbell_paid = data.get('videodoorbell_paid', False)

        # Import required functions and config
        from app.utils.simple_quote_products import (
            get_product_by_key, calculate_installation_cost,
            calculate_monthly_increase, PRICING_CONFIG
        )

        # Validate discount percentage based on payment terms
        max_discount_map = {
            '1_term': 25,
            '2_terms': 15,
            '3_terms': 10
        }
        max_discount = max_discount_map.get(payment_terms, 0)

        if discount_percentage > max_discount:
            return jsonify({
                "error": f"Discount cannot exceed {max_discount}% for {payment_terms} payment terms"
            }), 400

        # Calculate extra product costs (INCL BTW voor particulieren)
        extra_cost_incl_vat = 0
        extra_items_count = 0

        for product_key, quantity in extra_products.items():
            # Ensure quantity is a valid number
            if quantity is not None and isinstance(quantity, (int, float)) and quantity > 0:
                # Remove 'extra_' prefix if present
                clean_key = product_key.replace('extra_', '') if product_key.startswith('extra_') else product_key
                product = get_product_by_key(clean_key)
                if product and 'price_incl_vat' in product:
                    extra_cost_incl_vat += product['price_incl_vat'] * quantity
                    extra_items_count += int(quantity)

        # Calculate totals (INCL BTW voor particulieren)
        base_cost_incl_vat = PRICING_CONFIG['base_installation_cost_incl_vat']  # €999,99
        total_product_cost_incl_vat = base_cost_incl_vat + extra_cost_incl_vat

        # Calculate installation cost
        installation_cost = calculate_installation_cost(
            extra_cost_incl_vat,
            extra_items_count,
            videodoorbell_free,
            videodoorbell_paid
        )

        # Calculate monthly costs (INCL BTW)
        monthly_equipment = calculate_monthly_increase(extra_cost_incl_vat)
        monthly_monitoring = PRICING_CONFIG['monthly_monitoring']
        monthly_maintenance = PRICING_CONFIG['monthly_maintenance']
        total_monthly = monthly_equipment + monthly_monitoring + monthly_maintenance

        # Apply discount with minimum check
        discount_amount = installation_cost * (discount_percentage / 100)
        final_installation_cost = installation_cost - discount_amount

        # Minimum installatiekosten: €749,99 (25% korting op €999,99)
        minimum_cost = PRICING_CONFIG['base_installation_cost_incl_vat'] * 0.75
        final_installation_cost = max(final_installation_cost, minimum_cost)

        # Minimum maandelijkse kosten: €49,99
        minimum_monthly = PRICING_CONFIG['base_monthly_total']
        total_monthly = max(total_monthly, minimum_monthly)

        return jsonify({
            "calculations": {
                "base_cost_incl_vat": base_cost_incl_vat,
                "extra_cost_incl_vat": extra_cost_incl_vat,
                "total_product_cost_incl_vat": total_product_cost_incl_vat,
                "extra_items_count": extra_items_count,
                "installation_cost_before_discount": installation_cost,
                "discount_percentage": discount_percentage,
                "discount_amount": discount_amount,
                "final_installation_cost": final_installation_cost,
                "monthly_equipment_cost": monthly_equipment,
                "monthly_monitoring_cost": monthly_monitoring,
                "monthly_maintenance_cost": monthly_maintenance,
                "total_monthly_cost": total_monthly,
                "max_discount_allowed": max_discount
            }
        }), 200

    except Exception as e:
        logger.error(f"Failed to calculate offerte: {str(e)}")
        return jsonify({"error": str(e)}), 500

@simple_offerte_bp.route("/products", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("100/minute")
def get_products():
    """
    Get available products for offerte.
    Reuse existing products endpoint.
    """
    try:
        from app.controllers.simple_quote_controller import simple_quote_service
        from app.utils.simple_quote_products import get_products_for_category
        
        category = request.args.get("category", "all")
        
        if category == "all":
            from app.utils.simple_quote_products import BASE_ALARM_PACKAGE, EXTRA_PRODUCTS, CAMERA_PRODUCTS
            products = {
                "base_alarm_package": BASE_ALARM_PACKAGE,
                "extra_products": EXTRA_PRODUCTS,
                "camera_products": CAMERA_PRODUCTS
            }
        else:
            # Get products for specific category
            category_products = get_products_for_category(category)
            
            # Separate base package from extra products
            from app.utils.simple_quote_products import BASE_ALARM_PACKAGE
            base_package = {}
            extra_products = {}
            
            for key, product in category_products.items():
                if key in BASE_ALARM_PACKAGE:
                    base_package[key] = product
                else:
                    extra_products[key] = product
            
            products = {
                "base_package": base_package,
                "extra_products": extra_products
            }
        
        return jsonify({"products": products}), 200
        
    except Exception as e:
        logger.error(f"Failed to fetch products: {str(e)}")
        return jsonify({"error": str(e)}), 500
