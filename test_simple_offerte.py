#!/usr/bin/env python3
"""
Test script to verify the simple offerte approach works
"""
import sys
import os

def test_imports():
    """Test that all imports work correctly"""
    try:
        # Add backend to path
        sys.path.append('backend')
        
        print("🧪 Testing imports...")
        
        # Test service import
        from app.services.simple_offerte_service import SimpleOfferteService
        print("✅ SimpleOfferteService imported successfully")
        
        # Test controller import
        from app.controllers.simple_offerte_controller import simple_offerte_bp
        print("✅ simple_offerte_controller imported successfully")
        
        # Test that document types include 'offerte'
        from app.models.document import ALLOWED_DOCUMENT_TYPES
        if 'offerte' in ALLOWED_DOCUMENT_TYPES:
            print("✅ 'offerte' is in allowed document types")
        else:
            print("❌ 'offerte' is NOT in allowed document types")
            return False
        
        print("\n🎉 All imports working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_data_structure():
    """Test the data structure conversion"""
    try:
        print("\n🧪 Testing data structure...")
        
        # Test data
        test_offerte_data = {
            'customer_name': 'Test Customer',
            'customer_email': '<EMAIL>',
            'customer_phone': '123456789',
            'category': 'ALARM',
            'payment_terms': 'ONE_TERM',
            'discount_percentage': 10,
            'videodoorbell_free': False,
            'videodoorbell_paid': True,
            'extra_magneetcontact': 2,
            'extra_shock_sensor': 1,
            'signature_data': 'data:image/png;base64,test'
        }
        
        # Check required fields
        required_fields = ['customer_name', 'category', 'payment_terms']
        for field in required_fields:
            if field in test_offerte_data:
                print(f"✅ Required field '{field}' present")
            else:
                print(f"❌ Required field '{field}' missing")
                return False
        
        print("✅ Data structure is valid")
        return True
        
    except Exception as e:
        print(f"❌ Data structure error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Simple Offerte System...")
    
    success = True
    success &= test_imports()
    success &= test_data_structure()
    
    if success:
        print("\n🎉 Simple Offerte system is ready!")
        print("\n📋 Next steps:")
        print("1. Restart your backend server")
        print("2. Test the new endpoints:")
        print("   - POST /api/simple-offerte/create")
        print("   - POST /api/simple-offerte/calculate") 
        print("   - GET /api/simple-offerte/products")
        print("3. Update frontend to use simpleOfferteService")
        print("\n✨ This approach will:")
        print("   - Create/find customers automatically")
        print("   - Generate PDF using existing ReportLab code")
        print("   - Save as 'offerte' document type")
        print("   - Use existing document download system")
        print("   - No complex database enums to worry about!")
    else:
        print("\n💥 Simple Offerte system needs fixes!")
    
    sys.exit(0 if success else 1)
