/**
 * Simple Offerte Service
 * Uses existing document system instead of complex quote tables
 */

import api from '../api';

export interface OfferteData {
  customer_name: string;
  customer_email?: string;
  customer_phone?: string;
  customer_address?: string;
  customer_city?: string;
  customer_postal_code?: string;
  category: string;
  payment_terms: string;
  discount_percentage: number;
  videodoorbell_free: boolean;
  videodoorbell_paid: boolean;
  extra_magneetcontact: number;
  extra_shock_sensor: number;
  extra_pir_normaal: number;
  extra_rookmelder: number;
  extra_pircam: number;
  extra_bediendeel: number;
  extra_sirene: number;
  signature_data?: string;
}

export interface OfferteResult {
  success: boolean;
  message: string;
  customer: any;
  document: any;
  quote_number?: string;
}

export interface Product {
  name: string;
  description: string;
  price_excl_vat: number;
  price_incl_vat: number;
  image: string;
}

export interface QuoteCalculation {
  base_cost_excl_vat: number;
  extra_cost_excl_vat: number;
  total_product_cost_excl_vat: number;
  extra_items_count: number;
  installation_cost_before_discount: number;
  discount_percentage: number;
  discount_amount: number;
  final_installation_cost: number;
  monthly_equipment_cost: number;
  monthly_monitoring_cost: number;
  monthly_maintenance_cost: number;
  total_monthly_cost: number;
  max_discount_allowed: number;
}

class SimpleOfferteService {
  private baseUrl = '/simple-offerte';

  /**
   * Create offerte with customer and document
   */
  async createOfferte(data: OfferteData): Promise<OfferteResult> {
    try {
      const response = await api.post(`${this.baseUrl}/create`, data);
      return response.data;
    } catch (error: any) {
      console.error('Error creating offerte:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to create offerte. Please try again.');
    }
  }

  /**
   * Calculate offerte totals
   */
  async calculateOfferte(data: {
    extra_products: Record<string, number>;
    payment_terms: string;
    discount_percentage: number;
    videodoorbell_free: boolean;
    videodoorbell_paid: boolean;
  }): Promise<{ calculations: QuoteCalculation }> {
    try {
      const response = await api.post(`${this.baseUrl}/calculate`, data);
      return response.data;
    } catch (error: any) {
      console.error('Error calculating offerte:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to calculate offerte. Please try again.');
    }
  }

  /**
   * Get available products
   */
  async getProducts(category?: string): Promise<{ products: Record<string, any> }> {
    try {
      const url = category ? `${this.baseUrl}/products?category=${category}` : `${this.baseUrl}/products`;
      const response = await api.get(url);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching products:', error);
      if (error.response?.data?.error) {
        throw new Error(error.response.data.error);
      }
      throw new Error('Failed to fetch products. Please try again.');
    }
  }

  /**
   * Convert frontend values to backend format
   */
  convertToBackendFormat(category: string, paymentTerms: string): { category: string; payment_terms: string } {
    const categoryMap: Record<string, string> = {
      'alarm': 'ALARM',
      'cameras': 'CAMERAS', 
      'alarm_cameras': 'ALARM_CAMERAS'
    };
    
    const paymentMap: Record<string, string> = {
      '1_term': 'ONE_TERM',
      '2_terms': 'TWO_TERMS',
      '3_terms': 'THREE_TERMS'
    };
    
    return {
      category: categoryMap[category] || category,
      payment_terms: paymentMap[paymentTerms] || paymentTerms
    };
  }

  /**
   * Get product image URL
   */
  getProductImageUrl(filename: string): string {
    return `${this.baseUrl}/product-images/${filename}`;
  }
}

// Export singleton instance
export const simpleOfferteService = new SimpleOfferteService();
export default simpleOfferteService;
