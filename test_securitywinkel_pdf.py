#!/usr/bin/env python3
"""
Test script for De SecurityWinkel PDF generation.
This script tests the updated PDF service with De SecurityWinkel branding.
"""
import sys
import os
from datetime import datetime

def test_pdf_code_structure():
    """Test that the PDF service code has been updated correctly."""
    print("🧪 Testing PDF service code structure...")

    try:
        # Read the PDF service file
        pdf_service_path = os.path.join('backend', 'app', 'services', 'simple_quote_pdf_service.py')

        if not os.path.exists(pdf_service_path):
            print("❌ PDF service file not found!")
            return False

        with open(pdf_service_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Check for De SecurityWinkel branding
        checks = [
            ('De SecurityWinkel', 'Company name updated'),
            ('************', 'Phone number updated'),
            ('<EMAIL>', 'Support email updated'),
            ('<EMAIL>', 'Admin email updated'),
            ('Produktieweg 1, 6045 JC Roermond', 'Address updated'),
            ('© 2025, De SecurityWinkel', 'Copyright updated'),
            ('winkellogo.png', 'Logo path updated'),
            ('ervaring.png', 'Experience image added'),
            ('24uurperdagbewaking.png', 'Bewaking image added'),
            ('header table', 'Professional header layout'),
        ]

        passed_checks = 0
        for check_text, description in checks:
            if check_text in content:
                print(f"✅ {description}")
                passed_checks += 1
            else:
                print(f"❌ {description} - '{check_text}' not found")

        print(f"📊 Code structure: {passed_checks}/{len(checks)} checks passed")
        return passed_checks == len(checks)

    except Exception as e:
        print(f"❌ Code structure test failed: {str(e)}")
        return False

def test_store_images():
    """Test that store images exist."""
    print("\n🧪 Testing store images...")
    
    try:
        store_images_path = os.path.join('backend', 'app', 'static', 'images', 'store_information')
        
        required_images = [
            'winkellogo.png',
            'ervaring.png', 
            '24uurperdagbewaking.png'
        ]
        
        for image in required_images:
            image_path = os.path.join(store_images_path, image)
            if os.path.exists(image_path):
                print(f"✅ {image} found")
            else:
                print(f"❌ {image} missing at {image_path}")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ Store images test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing De SecurityWinkel PDF Updates...\n")

    tests = [
        test_store_images,
        test_pdf_code_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()  # Add spacing between tests
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! De SecurityWinkel PDF is ready!")
        print("\n📋 Changes made:")
        print("1. ✅ Company name changed to 'De SecurityWinkel'")
        print("2. ✅ Contact information updated")
        print("3. ✅ Footer with correct address and copyright")
        print("4. ✅ Logo integration from store_information folder")
        print("5. ✅ Experience/quality images added")
        print("6. ✅ Terms and conditions updated")
        print("\n🚀 Ready for production!")
        return 0
    else:
        print("\n💥 Some tests failed! Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
